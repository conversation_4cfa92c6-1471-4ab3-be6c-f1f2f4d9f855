import argparse
import wave


def wav_duration(path: str) -> float:
    with wave.open(path, "rb") as wav:
        frames = wav.getnframes()
        rate = wav.getframerate()
        channels = wav.getnchannels()
        duration = frames / float(rate * channels)
    return duration


def main():
    parser = argparse.ArgumentParser(
        description="Compare durations of two WAV files.")
    parser.add_argument("file1", help="Path to the first .wav file")
    parser.add_argument("file2", help="Path to the second .wav file")
    args = parser.parse_args()

    dur1 = wav_duration(args.file1)
    dur2 = wav_duration(args.file2)

    print(f"{args.file1} duration: {dur1:.2f} seconds")
    print(f"{args.file2} duration: {dur2:.2f} seconds")

    if dur1 > dur2:
        print(f"{args.file1} is longer by {dur1 - dur2:.2f} seconds")
    elif dur2 > dur1:
        print(f"{args.file2} is longer by {dur2 - dur1:.2f} seconds")
    else:
        print("Both files have the same duration")


if __name__ == "__main__":
    main()
