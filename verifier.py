import argparse
import wave


def wav_duration(path: str) -> float:
    with wave.open(path, "rb") as wav:
        frames = wav.getnframes()
        rate = wav.getframerate()
        channels = wav.getnchannels()
        duration = frames / float(rate)
    return duration


def format_duration(seconds: float) -> str:
    """Format duration in minutes:seconds.milliseconds format"""
    minutes = int(seconds // 60)
    remaining_seconds = seconds % 60
    secs = int(remaining_seconds)
    milliseconds = int((remaining_seconds - secs) * 1000)
    return f"{minutes}:{secs:02d}.{milliseconds:03d}"


def main():
    parser = argparse.ArgumentParser(
        description="Compare durations of two WAV files.")
    parser.add_argument("file1", help="Path to the first .wav file")
    parser.add_argument("file2", help="Path to the second .wav file")
    args = parser.parse_args()

    dur1 = wav_duration(args.file1)
    dur2 = wav_duration(args.file2)

    print(f"1 duration: {dur1:.2f} seconds")
    print(f"2 duration: {dur2:.2f} seconds")

    if dur1 > dur2:
        print(f"FILE 1 is longer by {dur1 - dur2:.2f} seconds")
        if dur1 - dur2 > 0.5:
            print("Dolby Atmos IS NOT OK")
            return 1
        else:
            print("Dolby Atmos IS OK")
            return 0
    elif dur2 > dur1:
        print(f"FILE 2 is longer by {dur2 - dur1:.2f} seconds")
        if dur2 - dur1 > 0.5:
            print("Dolby Atmos IS NOT OK")
            return 1
        else:
            print("Dolby Atmos IS OK")
            return 0
    else:
        print("Both files have the same duration")
        return 0


if __name__ == "__main__":
    main()
